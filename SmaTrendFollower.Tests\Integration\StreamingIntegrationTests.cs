using SmaTrendFollower.Services;
using FluentAssertions;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Xunit;
using DotNetEnv;

namespace SmaTrendFollower.Tests.Integration;

/// <summary>
/// Integration tests for streaming services
/// These tests require actual API keys and network connectivity
/// They are marked with [Fact(Skip = "Integration test")] to prevent running in CI
/// </summary>
public class StreamingIntegrationTests : IDisposable
{
    private readonly ServiceProvider _serviceProvider;
    private readonly IStreamingDataService _streamingService;

    public StreamingIntegrationTests()
    {
        // Load environment variables from .env file
        try
        {
            Env.Load("SmaTrendFollower.Console/.env");
        }
        catch
        {
            // Fallback: set the API keys directly for testing
            Environment.SetEnvironmentVariable("POLY_API_KEY", "stffXZCR90K0YULLv7zoUMq1k4JWiyHD");
            Environment.SetEnvironmentVariable("APCA_API_KEY_ID", "AKGBPW5HD8LVI5C6NJUJ");
            Environment.SetEnvironmentVariable("APCA_API_SECRET_KEY", "MdrZZtKbFtLQWNuggQMDqN12loiOheHWjSiWN2CM");
            Environment.SetEnvironmentVariable("APCA_API_ENV", "paper");
        }

        var services = new ServiceCollection();

        // Configure logging
        services.AddLogging(builder => builder.AddConsole().SetMinimumLevel(LogLevel.Information));

        // Configure configuration
        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();
        services.AddSingleton<IConfiguration>(configuration);
        
        // Register HTTP client factory
        services.AddHttpClient();
        
        // Register services
        services.AddSingleton<IAlpacaClientFactory, AlpacaClientFactory>();
        services.AddSingleton<IPolygonClientFactory, PolygonClientFactory>();
        services.AddSingleton<IStreamingDataService, StreamingDataService>();
        
        _serviceProvider = services.BuildServiceProvider();
        _streamingService = _serviceProvider.GetRequiredService<IStreamingDataService>();
    }

    [Fact]
    [Trait("Category", TestCategories.Integration)]
    [Trait("Category", TestCategories.Streaming)]
    [TestTimeout(TestTimeouts.Unit)]
    public void StreamingDataService_ShouldInitializeCorrectly()
    {
        // Skip if no valid API keys
        if (!TestConfiguration.ShouldRunIntegrationTests)
        {
            return;
        }

        // Arrange & Act
        var status = _streamingService.ConnectionStatus;

        // Assert
        status.Should().Be(StreamingConnectionStatus.Disconnected);
    }

    [Fact]
    [Trait("Category", TestCategories.Integration)]
    [Trait("Category", TestCategories.Alpaca)]
    [Trait("Category", TestCategories.Streaming)]
    [TestTimeout(TestTimeouts.Streaming)]
    public async Task ConnectAlpacaStreamAsync_WithValidCredentials_ShouldConnect()
    {
        // Skip if no valid Alpaca credentials
        if (!TestConfiguration.ShouldRunAlpacaTests)
        {
            return;
        }

        // Act
        await _streamingService.ConnectAlpacaStreamAsync();

        // Assert
        _streamingService.ConnectionStatus.Should().Be(StreamingConnectionStatus.Connected);

        // Cleanup
        await _streamingService.DisconnectAllAsync();
    }

    [Fact]
    [Trait("Category", TestCategories.Integration)]
    [Trait("Category", TestCategories.Polygon)]
    [Trait("Category", TestCategories.Streaming)]
    [TestTimeout(TestTimeouts.Streaming)]
    public async Task ConnectPolygonStreamAsync_WithValidCredentials_ShouldConnect()
    {
        // Skip if no valid Polygon credentials
        if (!TestConfiguration.ShouldRunPolygonTests)
        {
            return;
        }

        // Act
        await _streamingService.ConnectPolygonStreamAsync();

        // Assert
        // Note: Connection status is managed by the Polygon client, not the streaming service
        // We just verify no exception is thrown

        // Cleanup
        await _streamingService.DisconnectAllAsync();
    }

    [Fact(Skip = "Integration test - requires API keys")]
    public async Task SubscribeToQuotesAsync_WithValidSymbols_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "AAPL", "MSFT" };
        await _streamingService.ConnectAlpacaStreamAsync();

        // Act
        await _streamingService.SubscribeToQuotesAsync(symbols);

        // Assert
        // Since we're using placeholder implementation, just verify no exception is thrown

        // Cleanup
        await _streamingService.UnsubscribeAllAsync();
        await _streamingService.DisconnectAllAsync();
    }

    [Fact(Skip = "Integration test - requires API keys")]
    public async Task SubscribeToIndexUpdatesAsync_WithValidSymbols_ShouldSubscribe()
    {
        // Arrange
        var symbols = new[] { "I:VIX", "I:SPX" };
        await _streamingService.ConnectPolygonStreamAsync();

        // Act
        await _streamingService.SubscribeToIndexUpdatesAsync(symbols);

        // Assert
        // Since we're using placeholder implementation, just verify no exception is thrown

        // Cleanup
        await _streamingService.UnsubscribeAllAsync();
        await _streamingService.DisconnectAllAsync();
    }

    [Fact]
    public void StreamingDataService_EventHandlers_ShouldBeSubscribable()
    {
        // Arrange
        var quoteReceived = false;
        var barReceived = false;
        var indexUpdated = false;
        var tradeUpdated = false;

        _streamingService.QuoteReceived += (sender, args) => quoteReceived = true;
        _streamingService.BarReceived += (sender, args) => barReceived = true;
        _streamingService.IndexUpdated += (sender, args) => indexUpdated = true;
        _streamingService.TradeUpdated += (sender, args) => tradeUpdated = true;

        // Act & Assert
        // Events should be subscribable without throwing exceptions
        quoteReceived.Should().BeFalse(); // No events fired yet
        barReceived.Should().BeFalse();
        indexUpdated.Should().BeFalse();
        tradeUpdated.Should().BeFalse();
    }

    public void Dispose()
    {
        _streamingService?.Dispose();
        _serviceProvider?.Dispose();
    }
}

/// <summary>
/// Integration tests for Polygon WebSocket client
/// </summary>
public class PolygonWebSocketIntegrationTests : IDisposable
{
    private IPolygonWebSocketClient? _client;

    private IPolygonWebSocketClient CreateClient()
    {
        // Set the API key directly for testing
        Environment.SetEnvironmentVariable("POLY_API_KEY", "stffXZCR90K0YULLv7zoUMq1k4JWiyHD");

        var configuration = new ConfigurationBuilder()
            .AddEnvironmentVariables()
            .Build();

        var logger = LoggerFactory.Create(builder => builder.AddConsole())
            .CreateLogger<PolygonWebSocketClient>();

        return new PolygonWebSocketClient(configuration, logger);
    }

    [Fact(Skip = "Integration test - requires API key")]
    public async Task ConnectAsync_WithValidApiKey_ShouldConnect()
    {
        // Arrange
        _client = CreateClient();
        var connectionStatusChanged = false;
        _client.ConnectionStatusChanged += (sender, args) => connectionStatusChanged = true;

        // Act
        await _client.ConnectAsync();

        // Assert
        connectionStatusChanged.Should().BeTrue();

        // Cleanup
        await _client.DisconnectAsync();
    }

    [Fact(Skip = "Integration test - requires API key")]
    public async Task SubscribeToIndexUpdatesAsync_WithValidSymbols_ShouldReceiveUpdates()
    {
        // Arrange
        var indexUpdated = false;
        var symbols = new[] { "I:VIX" };
        
        _client.IndexUpdated += (sender, args) => indexUpdated = true;
        
        await _client.ConnectAsync();
        
        // Wait for authentication
        await Task.Delay(2000);

        // Act
        await _client.SubscribeToIndexUpdatesAsync(symbols);
        
        // Wait for potential updates
        await Task.Delay(5000);

        // Assert
        // Note: This may or may not receive updates depending on market hours
        // The test mainly verifies that subscription doesn't throw
        // indexUpdated may be true or false depending on market activity during test
        _ = indexUpdated; // Suppress unused variable warning

        // Cleanup
        await _client.UnsubscribeAllAsync();
        await _client.DisconnectAsync();
    }

    public void Dispose()
    {
        _client?.Dispose();
    }
}
